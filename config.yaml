# YOLOv8实时动作检测系统配置文件

# 模型配置
model:
  # 模型权重文件路径
  path: "scripts/models/yolov8s.pt"
  # 目标检测的最低置信度
  conf_threshold: 0.5
  # 图像尺寸
  img_size: 640
  # 推理设备 ('cpu', 'cuda', 'auto', '0', '1', etc.)
  # 'auto': 自动检测最佳设备
  # 'cpu': 强制使用CPU
  # 'cuda': 使用第一个GPU
  # '0', '1': 使用指定的GPU设备ID
  device: 'auto'

# 视频源配置
video:
  # 视频源（摄像头ID或文件路径）
  # 0 表示默认摄像头，也可以是视频文件路径
  source: 0
  # 视频帧率限制（可选）
  fps_limit: 30

# 动作检测阈值配置
thresholds:
  # 定义"手持有产品"的IoU阈值
  iou_held: 0.3
  # 定义"产品进入容器"的IoU阈值
  iou_in_container: 0.5
  # 定义"手与产品分离"的IoU阈值
  iou_release: 0.2
  # 动作触发后的冷却时间（秒）
  cooldown_seconds: 3.0

# 目标类别配置
classes:
  hand: 0
  product: 1
  blue_box: 2
  upper_yellow_box: 3
  lower_yellow_box: 4

# 输出配置
output:
  # 保存检测到动作时的图像
  save_images: true
  # 图像保存路径
  image_save_path: "output/detected_actions"
  # 日志文件路径
  log_file: "logs/action_detection.log"
  # 是否显示实时视频窗口
  show_video: true
  # 视频保存配置
  video_saving:
    # 是否启用视频保存功能
    enabled: true
    # 视频缓存大小（帧数，前15帧+触发帧+后15帧=31帧）
    buffer_size: 31
    # 基础保存路径
    base_path: "/home/<USER>/wolong_ws/yolo_detect_ws/data/results"
    # 动作类型保存路径
    action_paths:
      ok_action: "action_ok"
      nok_electric: "action_nok_electric"
      nok_appearance: "action_nok_appearence"

# 调试配置
debug:
  # 是否启用调试模式
  enabled: false
  # 是否显示边界框
  show_boxes: true
  # 是否显示置信度
  show_confidence: true
  # 是否显示状态信息
  show_state: true
