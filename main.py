#!/usr/bin/env python3
"""
YOLOv8实时动作检测系统主程序
基于YOLOv8与规则引擎的实时动作检测系统
"""

import argparse
import sys
import time
from pathlib import Path
from typing import Dict, Any
import numpy as np

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config import ConfigManager
from src.utils import setup_logger
from src.state_machine import ActionDetector
from loguru import logger


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="YOLOv8实时动作检测系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python main.py                           # 使用默认配置
  python main.py --config custom.yaml     # 使用自定义配置文件
  python main.py --video test.mp4         # 使用视频文件作为输入
  python main.py --camera 1               # 使用摄像头1
  python main.py --no-display             # 不显示视频窗口
  python main.py --debug --conf-threshold 0.25  # 调试模式，降低置信度阈值
  python main.py --device cuda                # 强制使用GPU
  python main.py --device cpu                 # 强制使用CPU

键盘控制:
  q/ESC: 退出程序
  r: 重置状态机
  d: 切换调试模式
        """
    )
    
    parser.add_argument(
        "--config", "-c",
        type=str,
        default="config.yaml",
        help="配置文件路径 (默认: config.yaml)"
    )
    
    parser.add_argument(
        "--video", "-v",
        type=str,
        help="视频文件路径 (覆盖配置文件中的video.source)"
    )
    
    parser.add_argument(
        "--camera",
        type=int,
        help="摄像头ID (覆盖配置文件中的video.source)"
    )
    
    parser.add_argument(
        "--model", "-m",
        type=str,
        help="模型文件路径 (覆盖配置文件中的model.path)"
    )
    
    parser.add_argument(
        "--no-display",
        action="store_true",
        help="不显示视频窗口"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="日志级别 (默认: INFO)"
    )
    
    parser.add_argument(
        "--output-dir", "-o",
        type=str,
        help="输出目录 (覆盖配置文件中的output.image_save_path)"
    )

    parser.add_argument(
        "--conf-threshold",
        type=float,
        help="检测置信度阈值 (覆盖配置文件中的设置，建议测试时使用0.25)"
    )

    parser.add_argument(
        "--debug",
        action="store_true",
        help="启用调试模式，显示详细的检测信息"
    )

    parser.add_argument(
        "--device",
        type=str,
        choices=['auto', 'cpu', 'cuda', '0', '1', '2', '3'],
        help="推理设备 (auto=自动检测, cpu=CPU, cuda=第一个GPU, 0/1/2/3=指定GPU ID)"
    )

    return parser.parse_args()


def action_callback(action_info: Dict[str, Any], frame: np.ndarray) -> None:
    """
    动作检测回调函数

    Args:
        action_info: 动作信息
        frame: 当前帧
    """
    action_type = action_info.get('action_type', 'unknown')
    container_type = action_info.get('container_type', 'unknown')

    # 根据动作类型显示不同的消息
    action_messages = {
        'ok_action': '✅ OK动作 - 产品放入蓝色箱子',
        'nok_electric': '⚡ 电气缺陷 - 产品放入上层黄色箱子',
        'nok_appearance': '👁️ 外观缺陷 - 产品放入下层黄色箱子'
    }

    message = action_messages.get(action_type, f'❓ 未知动作类型: {action_type}')

    logger.success(f"🎯 检测到动作 #{action_info['action_id']}")
    logger.info(f"   动作类型: {message}")
    logger.info(f"   容器类型: {container_type}")
    logger.info(f"   时间戳: {time.strftime('%H:%M:%S', time.localtime(action_info['timestamp']))}")
    logger.info(f"   跟踪时长: {action_info['tracked_object']['tracking_duration']:.2f}秒")
    logger.info(f"   置信度: {action_info['tracked_object']['confidence']:.3f}")

    if 'save_path' in action_info:
        logger.info(f"   保存路径: {action_info['save_path']}")

    # 这里可以添加其他处理逻辑，比如：
    # - 发送通知
    # - 记录到数据库
    # - 触发其他系统动作
    # - 播放声音提示等


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    try:
        # 加载配置
        logger.info(f"加载配置文件: {args.config}")
        config = ConfigManager(args.config)
        
        # 设置日志系统
        setup_logger(
            log_file=config.log_file,
            level=args.log_level
        )
        
        logger.info("=" * 60)
        logger.info("🚀 YOLOv8实时动作检测系统启动")
        logger.info("=" * 60)
        
        # 应用命令行参数覆盖
        video_source = config.video_source
        if args.video:
            video_source = args.video
            logger.info(f"使用命令行指定的视频文件: {video_source}")
        elif args.camera is not None:
            video_source = args.camera
            logger.info(f"使用命令行指定的摄像头: {video_source}")
        
        model_path = args.model if args.model else config.model_path
        output_dir = args.output_dir if args.output_dir else config.image_save_path
        conf_threshold = args.conf_threshold if args.conf_threshold is not None else config.conf_threshold
        device = args.device if args.device else config.get('model.device', 'auto')
        
        # 显示配置信息
        logger.info(f"📹 视频源: {video_source}")
        logger.info(f"🤖 模型路径: {model_path}")
        logger.info(f"📁 输出目录: {output_dir}")
        logger.info(f"🎯 置信度阈值: {conf_threshold}")
        logger.info(f"📊 IoU阈值 - 持有: {config.iou_held}, 容器: {config.iou_in_container}, 释放: {config.iou_release}")
        logger.info(f"⏰ 冷却时间: {config.cooldown_seconds}秒")

        if args.debug:
            logger.info("🐛 调试模式已启用")
        if args.conf_threshold is not None:
            logger.info(f"⚠️  使用命令行指定的置信度阈值: {conf_threshold} (原配置: {config.conf_threshold})")
        if args.device:
            logger.info(f"🖥️  使用命令行指定的设备: {device} (原配置: {config.get('model.device', 'auto')})")
        
        # 创建动作检测器
        detector = ActionDetector(
            model_path=model_path,
            video_source=video_source,
            conf_threshold=conf_threshold,
            iou_held_threshold=config.iou_held,
            iou_in_container_threshold=config.iou_in_container,
            iou_release_threshold=config.iou_release,
            cooldown_seconds=config.cooldown_seconds,
            fps_limit=config.fps_limit,
            save_images=config.save_images,
            image_save_path=output_dir,
            device=device,
            enable_video_saving=config.video_saving_enabled,
            video_buffer_size=config.video_buffer_size
        )

        # 设置调试模式
        if args.debug:
            detector.debug_mode = True

        # 显示设备和性能信息
        model_info = detector.detector.get_model_info()
        logger.info(f"🖥️  推理设备: {model_info['device']}")
        if model_info['device'] != 'cpu':
            if 'gpu_name' in model_info:
                logger.info(f"🎮 GPU: {model_info['gpu_name']}")
                logger.info(f"💾 GPU内存: {model_info.get('gpu_memory_total_mb', 0):.0f}MB")
        
        # 设置动作检测回调
        detector.set_action_callback(action_callback)
        
        # 显示控制提示
        if not args.no_display:
            logger.info("💡 控制提示:")
            logger.info("   按 'q' 或 ESC 退出程序")
            logger.info("   按 'r' 重置状态机")
            logger.info("   按 'd' 切换调试模式")
        
        logger.info("🔍 开始检测...")
        logger.info("-" * 60)
        
        # 运行检测
        detector.run(
            show_video=not args.no_display,
            window_name="YOLOv8 Action Detection"
        )
        
    except KeyboardInterrupt:
        logger.info("🛑 用户中断程序")
    except FileNotFoundError as e:
        logger.error(f"❌ 文件未找到: {e}")
        return 1
    except Exception as e:
        logger.error(f"❌ 程序运行出错: {e}")
        logger.exception("详细错误信息:")
        return 1
    finally:
        logger.info("=" * 60)
        logger.info("👋 程序结束")
        logger.info("=" * 60)
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
