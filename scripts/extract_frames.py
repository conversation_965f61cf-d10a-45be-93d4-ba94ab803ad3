#!/usr/bin/env python3
"""
视频帧提取脚本
遍历指定目录下的所有视频文件，每10帧截取一张图片并保存
"""

import os
import cv2
from pathlib import Path
import argparse
from tqdm import tqdm


def extract_frames_from_video(video_path, output_dir, frame_interval=10):
    """
    从单个视频文件中提取帧
    
    Args:
        video_path (str): 视频文件路径
        output_dir (str): 输出目录
        frame_interval (int): 帧间隔，默认每10帧提取一张
    
    Returns:
        int: 提取的帧数量
    """
    # 打开视频文件
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"无法打开视频文件: {video_path}")
        return 0
    
    # 获取视频基本信息
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    
    # 获取视频文件名（不含扩展名）
    video_name = Path(video_path).stem
    
    print(f"处理视频: {video_name}")
    print(f"总帧数: {total_frames}, FPS: {fps:.2f}")
    
    frame_count = 0
    extracted_count = 0
    
    # 创建进度条
    with tqdm(total=total_frames//frame_interval, desc=f"提取 {video_name}") as pbar:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # 每隔指定帧数保存一张图片
            if frame_count % frame_interval == 0:
                # 构造输出文件名
                output_filename = f"{video_name}_frame_{frame_count:06d}.jpg"
                output_path = os.path.join(output_dir, output_filename)
                
                # 保存帧
                cv2.imwrite(output_path, frame)
                extracted_count += 1
                pbar.update(1)
            
            frame_count += 1
    
    cap.release()
    print(f"从 {video_name} 中提取了 {extracted_count} 张图片")
    return extracted_count


def find_video_files(directory):
    """
    递归查找目录下的所有视频文件
    
    Args:
        directory (str): 搜索目录
    
    Returns:
        list: 视频文件路径列表
    """
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.m4v']
    video_files = []
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if any(file.lower().endswith(ext) for ext in video_extensions):
                video_files.append(os.path.join(root, file))
    
    return video_files


def main():
    parser = argparse.ArgumentParser(description='从视频中提取帧图片')
    parser.add_argument('--input_dir', '-i', 
                       default='data/short_videos',
                       help='输入视频目录 (默认: data/short_videos)')
    parser.add_argument('--output_dir', '-o',
                       default='data/extracted_frames',
                       help='输出图片目录 (默认: data/extracted_frames)')
    parser.add_argument('--frame_interval', '-f',
                       type=int, default=10,
                       help='帧间隔，每隔多少帧提取一张图片 (默认: 10)')
    
    args = parser.parse_args()
    
    # 确保输入目录存在
    if not os.path.exists(args.input_dir):
        print(f"错误: 输入目录不存在: {args.input_dir}")
        return
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    print(f"输出目录: {args.output_dir}")
    
    # 查找所有视频文件
    print(f"搜索视频文件: {args.input_dir}")
    video_files = find_video_files(args.input_dir)
    
    if not video_files:
        print("未找到任何视频文件！")
        return
    
    print(f"找到 {len(video_files)} 个视频文件")
    
    # 处理每个视频文件
    total_extracted = 0
    for i, video_path in enumerate(video_files, 1):
        print(f"\n[{i}/{len(video_files)}] 处理: {os.path.basename(video_path)}")
        extracted = extract_frames_from_video(video_path, args.output_dir, args.frame_interval)
        total_extracted += extracted
    
    print(f"\n处理完成！")
    print(f"总共处理了 {len(video_files)} 个视频文件")
    print(f"总共提取了 {total_extracted} 张图片")
    print(f"图片保存在: {args.output_dir}")


if __name__ == "__main__":
    main() 