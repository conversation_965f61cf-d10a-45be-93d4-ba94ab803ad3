# 视频帧提取脚本使用说明

## 功能介绍
`extract_frames.py` 脚本用于从视频文件中提取帧图片，支持：
- 递归遍历目录及子目录
- 支持多种视频格式 (.mp4, .avi, .mov, .mkv, .flv, .wmv, .m4v)
- 可自定义帧间隔
- 带进度条显示处理进度
- 自动创建输出目录

## 使用方法

### 基本用法（使用默认参数）
```bash
python scripts/extract_frames.py
```
- 默认输入目录：`data/short_videos`
- 默认输出目录：`data/extracted_frames`
- 默认帧间隔：每10帧提取一张

### 自定义参数
```bash
python scripts/extract_frames.py --input_dir data/short_videos --output_dir data/my_frames --frame_interval 5
```

### 参数说明
- `--input_dir` 或 `-i`: 输入视频目录（默认：data/short_videos）
- `--output_dir` 或 `-o`: 输出图片目录（默认：data/extracted_frames）
- `--frame_interval` 或 `-f`: 帧间隔，每隔多少帧提取一张图片（默认：10）

### 输出文件命名规则
提取的图片将按以下格式命名：
```
{视频文件名}_frame_{帧号:06d}.jpg
```
例如：
- `video1_frame_000000.jpg` (第0帧)
- `video1_frame_000010.jpg` (第10帧)
- `video1_frame_000020.jpg` (第20帧)

## 示例输出
```
输出目录: data/extracted_frames
搜索视频文件: data/short_videos
找到 150 个视频文件

[1/150] 处理: 20250727T075411Z_20250727T075911Z_decrypted-00.00.03.481-00.00.05.934-seg01_roi.mp4
处理视频: 20250727T075411Z_20250727T075911Z_decrypted-00.00.03.481-00.00.05.934-seg01_roi
总帧数: 73, FPS: 29.97
提取 20250727T075411Z_20250727T075911Z_decrypted-00.00.03.481-00.00.05.934-seg01_roi: 100%|██████████| 7/7 [00:00<00:00, 50.23it/s]
从 20250727T075411Z_20250727T075911Z_decrypted-00.00.03.481-00.00.05.934-seg01_roi 中提取了 8 张图片

处理完成！
总共处理了 150 个视频文件
总共提取了 1500 张图片
图片保存在: data/extracted_frames
```

## 注意事项
1. 确保已安装所有依赖包（opencv-python, tqdm等）
2. 输出目录会自动创建
3. 如果视频文件损坏或无法读取，脚本会跳过并继续处理其他文件
4. 提取的图片为JPG格式，压缩质量为OpenCV默认设置 