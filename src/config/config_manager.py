"""
配置管理器
负责加载和管理系统的所有配置参数
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path


class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径，默认为项目根目录下的config.yaml
        """
        if config_path is None:
            # 获取项目根目录
            project_root = Path(__file__).parent.parent.parent
            config_path = project_root / "config.yaml"
        
        self.config_path = Path(config_path)
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置字典
            
        Raises:
            FileNotFoundError: 配置文件不存在
            yaml.YAMLError: YAML格式错误
        """
        if not self.config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
            
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except yaml.YAMLError as e:
            raise yaml.YAMLError(f"配置文件格式错误: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值，支持点号分隔的嵌套键
        
        Args:
            key: 配置键，支持 'model.path' 这样的嵌套键
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
                
        return value
    
    def set(self, key: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
        """
        keys = key.split('.')
        config = self.config
        
        # 导航到最后一级的父字典
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置值
        config[keys[-1]] = value
    
    def save(self, path: Optional[str] = None) -> None:
        """
        保存配置到文件
        
        Args:
            path: 保存路径，默认为原配置文件路径
        """
        save_path = Path(path) if path else self.config_path
        
        with open(save_path, 'w', encoding='utf-8') as f:
            yaml.dump(self.config, f, default_flow_style=False, 
                     allow_unicode=True, indent=2)
    
    # 便捷属性访问方法
    @property
    def model_path(self) -> str:
        """模型路径"""
        return self.get('model.path', 'models/best.pt')
    
    @property
    def conf_threshold(self) -> float:
        """置信度阈值"""
        return self.get('model.conf_threshold', 0.5)
    
    @property
    def img_size(self) -> int:
        """图像尺寸"""
        return self.get('model.img_size', 640)
    
    @property
    def video_source(self) -> Any:
        """视频源"""
        return self.get('video.source', 0)
    
    @property
    def fps_limit(self) -> int:
        """帧率限制"""
        return self.get('video.fps_limit', 30)
    
    @property
    def iou_held(self) -> float:
        """手持有产品的IoU阈值"""
        return self.get('thresholds.iou_held', 0.3)
    
    @property
    def iou_in_container(self) -> float:
        """产品进入容器的IoU阈值"""
        return self.get('thresholds.iou_in_container', 0.5)
    
    @property
    def iou_release(self) -> float:
        """手与产品分离的IoU阈值"""
        return self.get('thresholds.iou_release', 0.2)
    
    @property
    def cooldown_seconds(self) -> float:
        """冷却时间"""
        return self.get('thresholds.cooldown_seconds', 3.0)
    
    @property
    def class_names(self) -> Dict[str, int]:
        """类别名称映射"""
        return self.get('classes', {
            'hand': 0,
            'product': 1,
            'blue_box': 2,
            'upper_yellow_box': 3,
            'lower_yellow_box': 4
        })
    
    @property
    def save_images(self) -> bool:
        """是否保存图像"""
        return self.get('output.save_images', True)
    
    @property
    def image_save_path(self) -> str:
        """图像保存路径"""
        return self.get('output.image_save_path', 'output/detected_actions')
    
    @property
    def log_file(self) -> str:
        """日志文件路径"""
        return self.get('output.log_file', 'logs/action_detection.log')
    
    @property
    def show_video(self) -> bool:
        """是否显示视频窗口"""
        return self.get('output.show_video', True)
    
    @property
    def debug_enabled(self) -> bool:
        """是否启用调试模式"""
        return self.get('debug.enabled', False)

    # 视频保存相关配置
    @property
    def video_saving_enabled(self) -> bool:
        """是否启用视频保存功能"""
        return self.get('output.video_saving.enabled', True)

    @property
    def video_buffer_size(self) -> int:
        """视频缓存大小"""
        return self.get('output.video_saving.buffer_size', 31)

    @property
    def video_base_path(self) -> str:
        """视频保存基础路径"""
        return self.get('output.video_saving.base_path', '/home/<USER>/wolong_ws/yolo_detect_ws/data/results')

    @property
    def video_action_paths(self) -> Dict[str, str]:
        """动作类型保存路径映射"""
        return self.get('output.video_saving.action_paths', {
            'ok_action': 'action_ok',
            'nok_electric': 'action_nok_electric',
            'nok_appearance': 'action_nok_appearence'
        })

    def __str__(self) -> str:
        """字符串表示"""
        return f"ConfigManager(config_path={self.config_path})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return self.__str__()
