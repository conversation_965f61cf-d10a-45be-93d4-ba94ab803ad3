"""
YOLOv8检测器
负责加载模型、执行推理和解析结果
"""

import cv2
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
from loguru import logger

try:
    from ultralytics import YOLO
except ImportError:
    logger.warning("ultralytics未安装，请运行: pip install ultralytics")
    YOLO = None


class YOLODetector:
    """YOLOv8检测器类"""
    
    def __init__(self, model_path: str, conf_threshold: float = 0.5,
                 img_size: int = 640, device: str = 'auto'):
        """
        初始化检测器

        Args:
            model_path: 模型权重文件路径
            conf_threshold: 置信度阈值
            img_size: 输入图像尺寸
            device: 推理设备 ('cpu', 'cuda', 'auto')
        """
        self.model_path = Path(model_path)
        self.conf_threshold = conf_threshold
        self.img_size = img_size
        self.device = self._get_device(device)
        self.model = None
        self.class_names = None

        self._load_model()

    def _get_device(self, device: str) -> str:
        """
        自动检测并返回可用的设备

        Args:
            device: 用户指定的设备 ('cpu', 'cuda', 'auto')

        Returns:
            实际使用的设备字符串
        """
        if device == 'auto':
            try:
                import torch
                logger.info(f"torch.cuda.is_available(): {torch.cuda.is_available()}")
                logger.info(f"torch.cuda.device_count(): {torch.cuda.device_count()}")

                if torch.cuda.is_available() and torch.cuda.device_count() > 0:
                    # 获取GPU信息
                    gpu_name = torch.cuda.get_device_name(0)
                    gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
                    logger.info(f"检测到GPU: {gpu_name} ({gpu_memory:.1f}GB)")

                    # 使用第一个GPU
                    device = '0'  # 使用设备ID而不是'cuda'
                    logger.info(f"使用GPU设备: cuda:{device}")
                else:
                    device = 'cpu'
                    logger.info(f"未检测到CUDA设备，使用CPU推理")
            except ImportError:
                device = 'cpu'
                logger.warning("PyTorch未安装，使用CPU推理")
        elif device == 'cuda':
            try:
                import torch
                if torch.cuda.is_available() and torch.cuda.device_count() > 0:
                    # 将'cuda'转换为设备ID
                    device = '0'
                    gpu_name = torch.cuda.get_device_name(0)
                    logger.info(f"使用指定的CUDA设备: {gpu_name}")
                else:
                    logger.warning("指定使用CUDA但未检测到CUDA设备，自动切换到CPU")
                    device = 'cpu'
            except ImportError:
                logger.warning("PyTorch未安装，无法使用CUDA，切换到CPU")
                device = 'cpu'

        logger.info(f"使用设备: {device}")

        # 如果使用GPU，进行GPU优化设置
        if device != 'cpu':
            try:
                import torch
                # 清理GPU缓存
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    logger.info("GPU缓存已清理")

                    # 设置GPU内存分配策略
                    torch.cuda.set_per_process_memory_fraction(0.8)  # 使用80%的GPU内存
                    logger.info("GPU内存分配策略已设置")
            except Exception as e:
                logger.warning(f"GPU优化设置失败: {e}")

        return device

    def _load_model(self) -> None:
        """加载YOLO模型"""
        if YOLO is None:
            raise ImportError("ultralytics未安装，无法加载YOLO模型")
        
        if not self.model_path.exists():
            logger.warning(f"模型文件不存在: {self.model_path}")
            logger.info("将使用预训练的YOLOv8n模型")
            self.model = YOLO('yolov8n.pt')
        else:
            logger.info(f"加载模型: {self.model_path}")
            self.model = YOLO(str(self.model_path))

        # 将模型移动到指定设备
        if self.device != 'cpu':
            try:
                logger.info(f"将模型移动到GPU设备: {self.device}")
                # YOLO模型会自动处理设备移动，但我们可以验证
                if hasattr(self.model, 'model') and hasattr(self.model.model, 'to'):
                    import torch
                    device_obj = torch.device(f'cuda:{self.device}' if self.device.isdigit() else self.device)
                    self.model.model.to(device_obj)
                    logger.info("模型已成功移动到GPU")
            except Exception as e:
                logger.warning(f"模型移动到GPU失败: {e}")
                logger.info("将使用CPU进行推理")
                self.device = 'cpu'
        
        # 获取类别名称
        if hasattr(self.model, 'names'):
            self.class_names = self.model.names
            logger.info(f"检测类别: {self.class_names}")
        else:
            # 默认类别名称（用于自定义模型）
            self.class_names = {
                0: 'hand',
                1: 'product',
                2: 'blue_box',
                3: 'upper_yellow_box',
                4: 'lower_yellow_box'
            }
            logger.info(f"使用默认类别名称: {self.class_names}")

        # 初始化性能监控
        self._inference_times = []
        self._gpu_memory_usage = []
    
    def detect(self, image: np.ndarray) -> Dict[str, List[Dict[str, Any]]]:
        """
        执行目标检测
        
        Args:
            image: 输入图像 (BGR格式)
            
        Returns:
            检测结果字典，格式为:
            {
                'hand': [{'bbox': [x1, y1, x2, y2], 'conf': 0.95}, ...],
                'product': [...],
                'blue_box': [...],
                'upper_yellow_box': [...],
                'lower_yellow_box': [...]
            }
        """
        if self.model is None:
            logger.error("模型未加载")
            return {}
        
        try:
            # 记录推理开始时间
            import time
            start_time = time.time()

            # 记录GPU内存使用（如果使用GPU）
            if self.device != 'cpu':
                try:
                    import torch
                    if torch.cuda.is_available():
                        torch.cuda.synchronize()  # 确保之前的操作完成
                        gpu_memory_before = torch.cuda.memory_allocated() / 1024**2  # MB
                except:
                    gpu_memory_before = 0

            # 执行推理
            results = self.model(
                image,
                conf=self.conf_threshold,
                imgsz=self.img_size,
                device=self.device,
                verbose=False
            )

            # 记录推理时间和GPU内存使用
            inference_time = time.time() - start_time
            self._inference_times.append(inference_time)

            if self.device != 'cpu':
                try:
                    import torch
                    if torch.cuda.is_available():
                        torch.cuda.synchronize()
                        gpu_memory_after = torch.cuda.memory_allocated() / 1024**2  # MB
                        self._gpu_memory_usage.append(gpu_memory_after)
                except:
                    pass

            # 解析结果
            detections = self._parse_results(results[0])

            # 添加调试信息（仅在前几次调用时输出）
            if not hasattr(self, '_debug_count'):
                self._debug_count = 0

            self._debug_count += 1
            if self._debug_count <= 5:
                total_detections = sum(len(objects) for objects in detections.values())
                avg_inference_time = sum(self._inference_times[-10:]) / min(len(self._inference_times), 10) * 1000  # ms

                logger.info(f"检测调试 #{self._debug_count}: 原始检测数={len(results[0].boxes) if results[0].boxes is not None else 0}, "
                           f"过滤后检测数={total_detections}, 置信度阈值={self.conf_threshold}")
                logger.info(f"推理时间: {inference_time*1000:.1f}ms, 平均: {avg_inference_time:.1f}ms")

                # GPU内存信息
                if self.device != 'cpu' and self._gpu_memory_usage:
                    current_memory = self._gpu_memory_usage[-1]
                    logger.info(f"GPU内存使用: {current_memory:.1f}MB")

                if results[0].boxes is not None and len(results[0].boxes) > 0:
                    logger.info(f"原始类别: {results[0].boxes.cls.cpu().numpy().tolist()}")
                    logger.info(f"原始置信度: {results[0].boxes.conf.cpu().numpy().tolist()}")

            return detections

        except Exception as e:
            logger.error(f"检测过程中出错: {e}")
            logger.exception("详细错误信息:")
            return {}
    
    def _parse_results(self, result) -> Dict[str, List[Dict[str, Any]]]:
        """
        解析YOLO检测结果
        
        Args:
            result: YOLO检测结果对象
            
        Returns:
            结构化的检测结果
        """
        detections = {
            'hand': [],
            'product': [],
            'blue_box': [],
            'upper_yellow_box': [],
            'lower_yellow_box': []
        }
        
        if result.boxes is None or len(result.boxes) == 0:
            return detections
        
        # 获取边界框、置信度和类别
        boxes = result.boxes.xyxy.cpu().numpy()  # [x1, y1, x2, y2]
        confidences = result.boxes.conf.cpu().numpy()
        classes = result.boxes.cls.cpu().numpy().astype(int)
        
        # 按类别组织检测结果
        for box, conf, cls in zip(boxes, confidences, classes):
            # 获取类别名称
            if cls in self.class_names:
                class_name = self.class_names[cls]
            else:
                # 尝试映射到我们的目标类别
                if cls == 0:
                    class_name = 'hand'
                elif cls == 1:
                    class_name = 'product'
                elif cls == 2:
                    class_name = 'blue_box'
                elif cls == 3:
                    class_name = 'upper_yellow_box'
                elif cls == 4:
                    class_name = 'lower_yellow_box'
                else:
                    continue  # 跳过未知类别
            
            # 只保留我们关心的类别
            if class_name in detections:
                detection = {
                    'bbox': box.tolist(),  # [x1, y1, x2, y2]
                    'conf': float(conf),
                    'class_id': int(cls)
                }
                detections[class_name].append(detection)
        
        return detections
    
    def draw_detections(self, image: np.ndarray, 
                       detections: Dict[str, List[Dict[str, Any]]],
                       show_conf: bool = True) -> np.ndarray:
        """
        在图像上绘制检测结果
        
        Args:
            image: 输入图像
            detections: 检测结果
            show_conf: 是否显示置信度
            
        Returns:
            绘制了检测框的图像
        """
        result_image = image.copy()
        
        # 定义颜色 (BGR格式)
        colors = {
            'hand': (0, 255, 0),            # 绿色
            'product': (255, 0, 0),         # 蓝色
            'blue_box': (255, 255, 0),      # 青色
            'upper_yellow_box': (0, 255, 255),  # 黄色
            'lower_yellow_box': (128, 0, 128)   # 紫色
        }
        
        for class_name, objects in detections.items():
            color = colors.get(class_name, (128, 128, 128))
            
            for obj in objects:
                bbox = obj['bbox']
                conf = obj['conf']
                
                # 绘制边界框
                x1, y1, x2, y2 = map(int, bbox)
                cv2.rectangle(result_image, (x1, y1), (x2, y2), color, 2)
                
                # 绘制标签
                label = class_name
                if show_conf:
                    label += f" {conf:.2f}"
                
                # 计算文本尺寸
                (text_width, text_height), _ = cv2.getTextSize(
                    label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2
                )
                
                # 绘制文本背景
                cv2.rectangle(
                    result_image,
                    (x1, y1 - text_height - 10),
                    (x1 + text_width, y1),
                    color,
                    -1
                )
                
                # 绘制文本
                cv2.putText(
                    result_image,
                    label,
                    (x1, y1 - 5),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.6,
                    (255, 255, 255),
                    2
                )
        
        return result_image

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息

        Returns:
            性能统计字典
        """
        stats = {
            'device': self.device,
            'total_inferences': len(self._inference_times),
            'avg_inference_time_ms': 0,
            'min_inference_time_ms': 0,
            'max_inference_time_ms': 0,
            'fps_estimate': 0
        }

        if self._inference_times:
            times_ms = [t * 1000 for t in self._inference_times]
            stats['avg_inference_time_ms'] = sum(times_ms) / len(times_ms)
            stats['min_inference_time_ms'] = min(times_ms)
            stats['max_inference_time_ms'] = max(times_ms)
            stats['fps_estimate'] = 1000 / stats['avg_inference_time_ms']

        # GPU相关统计
        if self.device != 'cpu':
            try:
                import torch
                if torch.cuda.is_available():
                    stats['gpu_name'] = torch.cuda.get_device_name(0)
                    stats['gpu_memory_total_mb'] = torch.cuda.get_device_properties(0).total_memory / 1024**2
                    stats['gpu_memory_allocated_mb'] = torch.cuda.memory_allocated() / 1024**2
                    stats['gpu_memory_cached_mb'] = torch.cuda.memory_reserved() / 1024**2

                    if self._gpu_memory_usage:
                        stats['avg_gpu_memory_usage_mb'] = sum(self._gpu_memory_usage) / len(self._gpu_memory_usage)
                        stats['max_gpu_memory_usage_mb'] = max(self._gpu_memory_usage)
            except Exception as e:
                logger.warning(f"获取GPU统计信息失败: {e}")

        return stats

    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息

        Returns:
            模型信息字典
        """
        info = {
            'model_path': str(self.model_path),
            'conf_threshold': self.conf_threshold,
            'img_size': self.img_size,
            'device': self.device,
            'class_names': self.class_names,
            'num_classes': len(self.class_names) if self.class_names else 0
        }

        # 添加性能统计
        info.update(self.get_performance_stats())

        return info
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            模型信息字典
        """
        if self.model is None:
            return {}
        
        info = {
            'model_path': str(self.model_path),
            'conf_threshold': self.conf_threshold,
            'img_size': self.img_size,
            'device': self.device,
            'class_names': self.class_names
        }
        
        return info
