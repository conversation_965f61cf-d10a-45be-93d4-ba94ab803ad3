"""
视频缓存模块
用于缓存视频帧，支持保存触发帧前后的视频片段
"""

import cv2
import numpy as np
import os
from collections import deque
from typing import Optional, List, Tuple
from pathlib import Path
from loguru import logger
import time


class VideoFrameBuffer:
    """视频帧缓存器"""
    
    def __init__(self, buffer_size: int = 31, fps: float = 30.0):
        """
        初始化视频帧缓存器
        
        Args:
            buffer_size: 缓存帧数（默认31帧，前15帧+触发帧+后15帧）
            fps: 视频帧率
        """
        self.buffer_size = buffer_size
        self.fps = fps
        self.frame_buffer = deque(maxlen=buffer_size)
        self.frame_timestamps = deque(maxlen=buffer_size)
        self.trigger_frame_index = buffer_size // 2  # 触发帧在缓存中的位置
        
        logger.info(f"视频缓存器初始化完成，缓存大小: {buffer_size}帧，帧率: {fps}fps")
    
    def add_frame(self, frame: np.ndarray, timestamp: float = None) -> None:
        """
        添加帧到缓存
        
        Args:
            frame: 视频帧
            timestamp: 时间戳，如果为None则使用当前时间
        """
        if timestamp is None:
            timestamp = time.time()
        
        # 复制帧以避免引用问题
        frame_copy = frame.copy()
        
        self.frame_buffer.append(frame_copy)
        self.frame_timestamps.append(timestamp)
    
    def is_ready_for_trigger(self) -> bool:
        """
        检查缓存是否准备好处理触发事件
        
        Returns:
            是否有足够的前置帧
        """
        return len(self.frame_buffer) >= self.trigger_frame_index + 1
    
    def save_action_video(self, save_path: str, action_id: str, 
                         trigger_timestamp: float) -> Optional[str]:
        """
        保存动作视频片段
        
        Args:
            save_path: 保存目录路径
            action_id: 动作ID
            trigger_timestamp: 触发时间戳
            
        Returns:
            保存的视频文件路径，失败返回None
        """
        if len(self.frame_buffer) < self.buffer_size:
            logger.warning(f"缓存帧数不足，当前: {len(self.frame_buffer)}, 需要: {self.buffer_size}")
            return None
        
        # 确保保存目录存在
        save_dir = Path(save_path)
        save_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成视频文件名
        timestamp_str = time.strftime("%Y%m%d_%H%M%S", time.localtime(trigger_timestamp))
        video_filename = f"{action_id}_{timestamp_str}.mp4"
        video_path = save_dir / video_filename
        
        try:
            # 获取帧尺寸
            if len(self.frame_buffer) == 0:
                logger.error("缓存为空，无法保存视频")
                return None
            
            first_frame = self.frame_buffer[0]
            height, width = first_frame.shape[:2]
            
            # 创建视频写入器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            video_writer = cv2.VideoWriter(
                str(video_path), 
                fourcc, 
                self.fps, 
                (width, height)
            )
            
            if not video_writer.isOpened():
                logger.error(f"无法创建视频写入器: {video_path}")
                return None
            
            # 写入所有缓存的帧
            frames_written = 0
            for frame in self.frame_buffer:
                video_writer.write(frame)
                frames_written += 1
            
            video_writer.release()
            
            logger.success(f"动作视频已保存: {video_path} ({frames_written}帧)")
            return str(video_path)
            
        except Exception as e:
            logger.error(f"保存视频时出错: {e}")
            return None
    
    def get_buffer_info(self) -> dict:
        """
        获取缓存信息
        
        Returns:
            缓存状态信息
        """
        return {
            'buffer_size': self.buffer_size,
            'current_frames': len(self.frame_buffer),
            'fps': self.fps,
            'trigger_frame_index': self.trigger_frame_index,
            'is_ready': self.is_ready_for_trigger(),
            'oldest_timestamp': self.frame_timestamps[0] if self.frame_timestamps else None,
            'newest_timestamp': self.frame_timestamps[-1] if self.frame_timestamps else None
        }
    
    def clear_buffer(self) -> None:
        """清空缓存"""
        self.frame_buffer.clear()
        self.frame_timestamps.clear()
        logger.debug("视频缓存已清空")


class ActionVideoSaver:
    """动作视频保存器"""
    
    def __init__(self, base_save_path: str = "/home/<USER>/wolong_ws/yolo_detect_ws/data/results"):
        """
        初始化动作视频保存器
        
        Args:
            base_save_path: 基础保存路径
        """
        self.base_save_path = Path(base_save_path)
        self.action_paths = {
            'ok_action': self.base_save_path / 'action_ok',
            'nok_electric': self.base_save_path / 'action_nok_electric', 
            'nok_appearance': self.base_save_path / 'action_nok_appearence'
        }
        
        # 创建所有必要的目录
        for path in self.action_paths.values():
            path.mkdir(parents=True, exist_ok=True)
            logger.info(f"确保目录存在: {path}")
    
    def get_save_path(self, action_type: str) -> str:
        """
        根据动作类型获取保存路径
        
        Args:
            action_type: 动作类型
            
        Returns:
            保存路径
        """
        return str(self.action_paths.get(action_type, self.action_paths['ok_action']))
    
    def save_action_video(self, video_buffer: VideoFrameBuffer, 
                         action_info: dict) -> Optional[str]:
        """
        保存动作视频
        
        Args:
            video_buffer: 视频缓存器
            action_info: 动作信息
            
        Returns:
            保存的视频文件路径
        """
        action_type = action_info.get('action_type', 'ok_action')
        action_id = action_info.get('action_id', 'unknown')
        timestamp = action_info.get('timestamp', time.time())
        
        save_path = self.get_save_path(action_type)
        
        return video_buffer.save_action_video(save_path, action_id, timestamp)
