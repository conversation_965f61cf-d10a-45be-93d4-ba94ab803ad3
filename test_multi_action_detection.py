#!/usr/bin/env python3
"""
多动作类型检测系统测试脚本
测试系统能否正确识别3种不同的动作类型并保存对应的视频片段
"""

import sys
import time
import numpy as np
from pathlib import Path
from loguru import logger

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.state_machine.action_state_machine import ActionStateMachine, ActionState, ActionType
from src.utils.video_buffer import VideoFrameBuffer, ActionVideoSaver
from src.utils.geometry import calculate_iou


def test_action_type_determination():
    """测试动作类型判断逻辑"""
    logger.info("🧪 测试动作类型判断逻辑...")
    
    # 创建状态机
    sm = ActionStateMachine()
    
    # 测试不同容器类型的动作类型判断
    test_cases = [
        ('blue_box', ActionType.OK_ACTION),
        ('upper_yellow_box', ActionType.NOK_ELECTRIC),
        ('lower_yellow_box', ActionType.NOK_APPEARANCE),
        (None, ActionType.OK_ACTION),  # 默认情况
        ('unknown_container', ActionType.OK_ACTION)  # 未知容器类型
    ]
    
    for container_type, expected_action_type in test_cases:
        action_type = sm._determine_action_type(container_type)
        assert action_type == expected_action_type, f"容器类型 {container_type} 的动作类型判断错误"
        logger.success(f"✅ {container_type} -> {action_type.value}")
    
    logger.success("动作类型判断逻辑测试通过")


def test_save_path_generation():
    """测试保存路径生成"""
    logger.info("🧪 测试保存路径生成...")
    
    sm = ActionStateMachine()
    
    test_cases = [
        (ActionType.OK_ACTION, "action_ok"),
        (ActionType.NOK_ELECTRIC, "action_nok_electric"),
        (ActionType.NOK_APPEARANCE, "action_nok_appearence")
    ]
    
    for action_type, expected_path_suffix in test_cases:
        save_path = sm._get_action_save_path(action_type)
        assert expected_path_suffix in save_path, f"保存路径生成错误: {save_path}"
        logger.success(f"✅ {action_type.value} -> {save_path}")
    
    logger.success("保存路径生成测试通过")


def test_video_buffer():
    """测试视频缓存功能"""
    logger.info("🧪 测试视频缓存功能...")
    
    # 创建视频缓存器
    buffer = VideoFrameBuffer(buffer_size=31, fps=30.0)
    
    # 创建模拟帧
    test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # 测试帧添加
    for i in range(35):  # 添加超过缓存大小的帧数
        buffer.add_frame(test_frame, time.time() + i * 0.033)
    
    # 检查缓存状态
    buffer_info = buffer.get_buffer_info()
    assert buffer_info['current_frames'] == 31, f"缓存帧数错误: {buffer_info['current_frames']}"
    assert buffer_info['is_ready'], "缓存未准备好"
    
    logger.success(f"✅ 视频缓存测试通过，当前帧数: {buffer_info['current_frames']}")


def test_action_video_saver():
    """测试动作视频保存器"""
    logger.info("🧪 测试动作视频保存器...")
    
    # 创建临时保存路径
    temp_base_path = Path("temp_test_results")
    saver = ActionVideoSaver(str(temp_base_path))
    
    # 测试路径获取
    test_cases = [
        ('ok_action', 'action_ok'),
        ('nok_electric', 'action_nok_electric'),
        ('nok_appearance', 'action_nok_appearence'),
        ('unknown_action', 'action_ok')  # 默认路径
    ]
    
    for action_type, expected_dir in test_cases:
        save_path = saver.get_save_path(action_type)
        assert expected_dir in save_path, f"保存路径错误: {save_path}"
        logger.success(f"✅ {action_type} -> {save_path}")
    
    # 清理临时目录
    import shutil
    if temp_base_path.exists():
        shutil.rmtree(temp_base_path)
    
    logger.success("动作视频保存器测试通过")


def test_complete_action_detection_flow():
    """测试完整的动作检测流程"""
    logger.info("🧪 测试完整的动作检测流程...")
    
    # 创建状态机
    sm = ActionStateMachine(
        iou_held_threshold=0.3,
        iou_in_container_threshold=0.5,
        iou_release_threshold=0.2,
        cooldown_seconds=1.0,
        min_tracking_time=0.1  # 缩短测试时间
    )
    
    # 测试场景：手持产品放入蓝色箱子（OK动作）
    logger.info("测试场景1: OK动作 - 放入蓝色箱子")
    
    # 1. 空闲状态 -> 检测到手持产品
    detections_held = {
        'hand': [{'bbox': [10, 10, 20, 20], 'conf': 0.9}],
        'product': [{'bbox': [12, 12, 18, 18], 'conf': 0.8}],  # 与手重叠
        'blue_box': [],
        'upper_yellow_box': [],
        'lower_yellow_box': []
    }
    
    result = sm.update(detections_held)
    assert result is None, "空闲状态不应返回动作信息"
    assert sm.current_state == ActionState.TRACKING_HELD_OBJECT, "状态应转为跟踪状态"
    logger.success("✅ 检测到手持产品，状态转为跟踪")
    
    # 2. 跟踪状态 -> 产品进入蓝色箱子
    time.sleep(0.1)  # 等待一点时间
    detections_in_blue_box = {
        'hand': [{'bbox': [10, 10, 20, 20], 'conf': 0.9}],
        'product': [{'bbox': [13, 13, 19, 19], 'conf': 0.8}],  # 产品位置稍微移动，保持高IoU
        'blue_box': [{'bbox': [10, 10, 25, 25], 'conf': 0.9}],  # 蓝色箱子包含产品
        'upper_yellow_box': [],
        'lower_yellow_box': []
    }
    
    result = sm.update(detections_in_blue_box)
    assert result is None, "跟踪状态不应返回动作信息"
    assert sm.current_state == ActionState.EVALUATING_RELEASE, "状态应转为评估释放"
    assert sm.tracked_object.target_container_type == 'blue_box', "目标容器类型应为blue_box"
    logger.success("✅ 产品进入蓝色箱子，状态转为评估释放")
    
    # 3. 评估释放状态 -> 手与产品分离，动作完成
    time.sleep(0.2)  # 确保满足最小跟踪时间
    detections_released = {
        'hand': [{'bbox': [5, 5, 15, 15], 'conf': 0.9}],  # 手远离产品
        'product': [{'bbox': [14, 14, 20, 20], 'conf': 0.8}],  # 产品保持在箱子内，位置与跟踪对象接近
        'blue_box': [{'bbox': [10, 10, 25, 25], 'conf': 0.9}],
        'upper_yellow_box': [],
        'lower_yellow_box': []
    }
    
    result = sm.update(detections_released)
    assert result is not None, "应该检测到动作完成"
    assert result['action_type'] == ActionType.OK_ACTION.value, "动作类型应为OK动作"
    assert result['container_type'] == 'blue_box', "容器类型应为blue_box"
    assert 'action_ok' in result['save_path'], "保存路径应包含action_ok"
    logger.success(f"✅ OK动作检测完成: {result['action_id']}")
    
    # 重置状态机测试其他动作类型
    sm.reset()
    
    # 测试场景2：电气缺陷动作 - 放入上层黄色箱子
    logger.info("测试场景2: 电气缺陷动作 - 放入上层黄色箱子")
    
    # 快速模拟完整流程
    sm.update(detections_held)  # 手持产品
    time.sleep(0.1)
    
    detections_in_upper_yellow = {
        'hand': [{'bbox': [10, 10, 20, 20], 'conf': 0.9}],
        'product': [{'bbox': [16, 16, 26, 26], 'conf': 0.8}],  # 保持可跟踪的位置
        'blue_box': [],
        'upper_yellow_box': [{'bbox': [14, 14, 28, 28], 'conf': 0.9}],  # 包含产品
        'lower_yellow_box': []
    }

    sm.update(detections_in_upper_yellow)  # 进入上层黄色箱子
    time.sleep(0.2)

    detections_released_upper = {
        'hand': [{'bbox': [5, 5, 15, 15], 'conf': 0.9}],  # 手远离产品
        'product': [{'bbox': [17, 17, 27, 27], 'conf': 0.8}],  # 产品在箱子内
        'blue_box': [],
        'upper_yellow_box': [{'bbox': [14, 14, 28, 28], 'conf': 0.9}],
        'lower_yellow_box': []
    }
    
    result = sm.update(detections_released_upper)
    assert result is not None, "应该检测到电气缺陷动作"
    assert result['action_type'] == ActionType.NOK_ELECTRIC.value, "动作类型应为电气缺陷"
    assert 'action_nok_electric' in result['save_path'], "保存路径应包含action_nok_electric"
    logger.success(f"✅ 电气缺陷动作检测完成: {result['action_id']}")
    
    logger.success("完整动作检测流程测试通过")


def main():
    """主测试函数"""
    logger.info("🚀 开始多动作类型检测系统测试")
    logger.info("=" * 60)
    
    try:
        # 运行各项测试
        test_action_type_determination()
        test_save_path_generation()
        test_video_buffer()
        test_action_video_saver()
        test_complete_action_detection_flow()
        
        logger.success("🎉 所有测试通过！")
        logger.info("系统已准备好检测3种不同的动作类型：")
        logger.info("  1. ✅ OK动作 - 产品放入蓝色箱子")
        logger.info("  2. ⚡ 电气缺陷 - 产品放入上层黄色箱子")
        logger.info("  3. 👁️ 外观缺陷 - 产品放入下层黄色箱子")
        logger.info("视频片段将自动保存到对应的分类目录中")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        logger.exception("详细错误信息:")
        return 1
    
    logger.info("=" * 60)
    return 0


if __name__ == "__main__":
    sys.exit(main())
